import pandas as pd

try:
    import spotfire
    platform = 'spotfire'
except:
    platform = 'ide'
    meas_items_df = pd.read_csv('量测项目名称.csv').astype('string')
    meas_data_df = pd.read_csv('ADS_V_CF_RESULT.csv')
    meas_data_df['STEP_ID'] = meas_data_df['STEP_ID'].astype('string')
    step_id = '202000'


def apply_operation(group, operation):
    """根据操作类型对PARAM_VALUE进行聚合"""
    if operation == 'mean':
        return group['PARAM_VALUE'].mean()
    elif operation == 'range':
        return group['PARAM_VALUE'].max() - group['PARAM_VALUE'].min()
    elif operation == 'max':
        return group['PARAM_VALUE'].max()
    elif operation == 'min':
        return group['PARAM_VALUE'].min()
    elif operation == 'start':
        # 筛选Y为负的行
        negative_y = group[group['Y'] < 0]
        return negative_y['PARAM_VALUE'].max() - negative_y['PARAM_VALUE'].min() if not negative_y.empty else None
    elif operation == 'end':
        # 对Y为正的行进行计算
        positive_y = group[group['Y'] > 0]
        return positive_y['PARAM_VALUE'].max() - positive_y['PARAM_VALUE'].min() if not positive_y.empty else None
    else:
        return None


def process_meas_name(meas_name, operation, step_ids, param_names, meas_data_df):
    """处理单个MEAS_NAME，生成对应的DataFrame"""
    # 解析允许的STEP_ID和PARAM_NAME，始终假设数据结构应完整
    allowed_step_ids = [s.strip() for s in step_ids.split('/')]
    allowed_param_names = [p.strip() for p in param_names.split('/')]

    # 筛选数据
    filtered_data = meas_data_df[
        (meas_data_df['STEP_ID'].isin(allowed_step_ids)) &
        (meas_data_df['PARAM_NAME'].isin(allowed_param_names))
    ].copy()

    if filtered_data.empty:
        print(f"警告: {meas_name} 没有找到匹配的数据")
        # 返回一个空的DataFrame，但提前用结构说明需要的列（其中MEAS_NAME为计算的列）
        return pd.DataFrame(columns=['PRODUCT_ID', 'STEP_ID', 'GLASS_ID', 'GLASS_END_TIME', 'PARAM_NAME', meas_name])

    groupby_cols = ['PRODUCT_ID', 'STEP_ID', 'GLASS_ID', 'GLASS_END_TIME', 'PARAM_NAME']
    missing_cols = [col for col in groupby_cols if col not in filtered_data.columns]
    if missing_cols:
        print(f"警告: {meas_name} 缺少必要的列: {missing_cols}")
        return pd.DataFrame(columns=['PRODUCT_ID', 'STEP_ID', 'GLASS_ID', 'GLASS_END_TIME', 'PARAM_NAME', meas_name])

    if operation in ['start', 'end'] and 'Y' not in filtered_data.columns:
        print(f"警告: {meas_name} 的操作 '{operation}' 需要Y列，但数据中不存在")
        return pd.DataFrame(columns=['PRODUCT_ID', 'STEP_ID', 'GLASS_ID', 'GLASS_END_TIME', 'PARAM_NAME', meas_name])

    # 分组并应用聚合操作
    result = filtered_data.groupby(groupby_cols).apply(lambda x: apply_operation(x, operation)).reset_index()
    result.rename(columns={0: meas_name}, inplace=True)
    # 剔除计算结果为空的行
    result = result.dropna(subset=[meas_name])
    # 采用固定规则去重，保证数据结构正确
    result = result.sort_values('GLASS_END_TIME').drop_duplicates(
        subset=['PRODUCT_ID', 'GLASS_ID'],
        keep='last'
    )

    return result


def main():
    """按MAIN_STEP筛选并处理各量测项目"""
    target_items = meas_items_df[meas_items_df['MAIN_STEP'] == step_id].copy()

    if target_items.empty:
        print(f"没有找到MAIN_STEP为 {step_id} 的量测项目")
        return {}

    print(f"找到 {len(target_items)} 个量测项目需要处理")

    # 存储所有结果的字典
    results = {}

    # 逐个处理每个MEAS_NAME
    for idx, row in target_items.iterrows():
        meas_name = row['MEAS_NAME']
        operation = row['OPERATION']
        step_ids = row['STEP_ID']
        param_names = row['PARAM_NAME']

        print(f"正在处理: {meas_name} (操作: {operation})")

        # 处理当前MEAS_NAME
        result_df = process_meas_name(
            meas_name=meas_name,
            operation=operation,
            step_ids=step_ids,
            param_names=param_names,
            meas_data_df=meas_data_df
        )

        if not result_df.empty:
            results[meas_name] = result_df
            print(f"  完成: {meas_name}, 生成 {len(result_df)} 行数据")
        else:
            print(f"  警告: {meas_name} 没有生成数据")

    return results


def merge_all_results(results, target_items):
    """
    合并所有DataFrame，按照 meas_items_df 的顺序。
    基于数据结构设计，提前定义完整的表头，使空数据天然返回包含表头的DataFrame，
    而无需特殊的if/else处理。
    """
    ordered_meas_names = target_items['MEAS_NAME'].tolist()
    final_columns = ['PRODUCT_ID', 'GLASS_ID'] + ordered_meas_names

    # 初始化一个空DataFrame，其列结构铺垫完整
    base = pd.DataFrame(columns=final_columns)

    # 对于已有计算结果的量测项目，通过outer join合并数据
    for meas_name in ordered_meas_names:
        if meas_name in results:
            # 选择需要的列进行合并
            df_to_merge = results[meas_name][['PRODUCT_ID', 'GLASS_ID', meas_name]].copy()
            base = pd.merge(base, df_to_merge, on=['PRODUCT_ID', 'GLASS_ID'], how='outer')

    # 确保每个目标列都存在
    for col in final_columns:
        if col not in base.columns:
            # 给产品和玻璃ID用string类型，其他默认Float64
            base[col] = pd.Series(dtype="string" if col in ['PRODUCT_ID', 'GLASS_ID'] else "Float64")

    # 设置数据类型
    base['PRODUCT_ID'] = base['PRODUCT_ID'].astype('string')
    base['GLASS_ID'] = base['GLASS_ID'].astype('string')
    for col in base.columns:
        if col not in ['PRODUCT_ID', 'GLASS_ID']:
            # 注意：即使转换失败也不要抛出异常
            base[col] = base[col].astype('Float64', errors='ignore')

    # 调整列顺序
    base = base[final_columns]
    print(f"合并完成: {len(base)} 行 x {len(base.columns)} 列")
    print(f"列顺序: {list(base.columns)}")
    return base


# 主处理逻辑调用
processed_results = main()
target_items = meas_items_df[meas_items_df['MAIN_STEP'] == step_id].copy()
final_merged_df = merge_all_results(processed_results, target_items)

import datetime
time_trigger = datetime.datetime.now()

# 输出结果：即使最终数据为空，也能产生包含完整表头的文件
if platform == 'ide':
    # 保存各单独结果
    for result in processed_results:
        processed_results[result].to_csv(f'results/{result}.csv', index=False)

    # 保存合并结果
    final_merged_df.to_csv('results/merged_results.csv', index=False)
    if not final_merged_df.empty:
        print("合并结果已保存到 results/merged_results.csv")
        print("\n合并结果概览:")
        print(f"总行数: {len(final_merged_df)}")
        print("列信息:")
        for col in final_merged_df.columns:
            non_null_count = final_merged_df[col].notna().sum()
            print(f"  {col}: {non_null_count} 非空值 ({non_null_count / len(final_merged_df) * 100 if len(final_merged_df) else 0:.1f}%)")
        print("\n前5行数据:")
        print(final_merged_df.head())
    else:
        print("没有数据可以合并，但文件已保存（仅包含表头）。")
