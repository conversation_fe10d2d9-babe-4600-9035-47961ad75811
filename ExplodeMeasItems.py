import pandas as pd

try:
    import spotfire
    platform = 'spotfire'
    main_step = step_df.loc[0, 0]
except:
    platform = 'ide'
    meas_items_df = pd.read_excel('量测项目名称.csv')
    main_step = '202000'

meas_items_df = meas_items_df.astype('string')
meas_items_df = meas_items_df[meas_items_df['MAIN_STEP'] == main_step].reset_index(drop=True)

df_exploded_step = (meas_items_df.assign(STEP_ID=meas_items_df['STEP_ID'].str.split('/'))
               .explode('STEP_ID').reset_index(drop=True))
df_exploded_param = (df_exploded_step.assign(PARAM_NAME=df_exploded_step['PARAM_NAME'].str.split('/'))
               .explode('PARAM_NAME').reset_index(drop=True))

df_exploded_keep = df_exploded_param[['MAIN_STEP', 'STEP_ID', 'STEP_GROUP', 'PARAM_NAME']]

df_deduped = df_exploded_keep.drop_duplicates()

if platform == 'ide':
    df_deduped.to_excel('results/exploded_meas_items.xlsx', index=False)
